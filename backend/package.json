{"name": "@aquabell/backend", "version": "1.0.0", "description": "Supabase Edge Functions for AquaBell hydration reminders", "scripts": {"dev": "supabase start", "stop": "supabase stop", "reset": "supabase db reset", "migrate": "supabase db push", "generate-types": "supabase gen types typescript --local > types/supabase.ts", "deploy": "supabase functions deploy", "deploy:send-reminders": "supabase functions deploy send-reminders", "logs": "supabase functions logs", "logs:send-reminders": "supabase functions logs send-reminders"}, "dependencies": {"@aquabell/shared": "file:../shared"}, "devDependencies": {"@types/node": "^20.6.0", "typescript": "^5.2.2"}}