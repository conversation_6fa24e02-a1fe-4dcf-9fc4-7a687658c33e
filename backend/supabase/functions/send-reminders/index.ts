import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { corsHeaders } from '../_shared/cors.ts'

interface UserForReminder {
  user_id: string;
  fcm_token: string;
  timezone: string;
  wake_time: string;
  sleep_time: string;
  reminder_interval_minutes: number;
  last_reminder_sent: string;
}

interface FCMMessage {
  to: string;
  notification: {
    title: string;
    body: string;
    icon: string;
    badge: string;
  };
  data: {
    type: string;
    timestamp: string;
  };
  webpush: {
    headers: {
      TTL: string;
    };
    notification: {
      icon: string;
      badge: string;
      requireInteraction: boolean;
      tag: string;
      actions: Array<{
        action: string;
        title: string;
      }>;
    };
  };
}

const HYDRATION_REMINDERS = [
  {
    title: "Time to hydrate! 💧",
    body: "Your body is calling for some refreshing water!",
    emoji: "💧"
  },
  {
    title: "Water break! 🌊",
    body: "Take a moment to drink some water and feel refreshed!",
    emoji: "🌊"
  },
  {
    title: "Hydration station! 🚰",
    body: "Keep your energy up with a nice glass of water!",
    emoji: "🚰"
  },
  {
    title: "Drink up, buttercup! 🌻",
    body: "Your future self will thank you for staying hydrated!",
    emoji: "🌻"
  },
  {
    title: "H2O time! 💦",
    body: "Let's keep those hydration levels topped up!",
    emoji: "💦"
  }
];

function getRandomReminder() {
  return HYDRATION_REMINDERS[Math.floor(Math.random() * HYDRATION_REMINDERS.length)];
}

function isWithinActiveHours(
  wakeTime: string,
  sleepTime: string,
  timezone: string,
  currentTime?: Date
): boolean {
  const now = currentTime || new Date();
  
  // Convert to user's timezone
  const userTime = new Date(now.toLocaleString("en-US", { timeZone: timezone }));
  const currentHour = userTime.getHours();
  const currentMinute = userTime.getMinutes();
  const currentTimeInMinutes = currentHour * 60 + currentMinute;

  // Parse wake and sleep times
  const [wakeHour, wakeMinute] = wakeTime.split(':').map(Number);
  const [sleepHour, sleepMinute] = sleepTime.split(':').map(Number);
  
  const wakeTimeInMinutes = wakeHour * 60 + wakeMinute;
  const sleepTimeInMinutes = sleepHour * 60 + sleepMinute;

  // Handle case where sleep time is next day (e.g., wake: 07:00, sleep: 23:00)
  if (sleepTimeInMinutes > wakeTimeInMinutes) {
    return currentTimeInMinutes >= wakeTimeInMinutes && currentTimeInMinutes <= sleepTimeInMinutes;
  } else {
    // Handle case where sleep time crosses midnight (e.g., wake: 22:00, sleep: 06:00)
    return currentTimeInMinutes >= wakeTimeInMinutes || currentTimeInMinutes <= sleepTimeInMinutes;
  }
}

async function sendFCMNotification(
  fcmToken: string,
  title: string,
  body: string,
  serverKey: string
): Promise<{ success: boolean; messageId?: string; error?: string }> {
  const message: FCMMessage = {
    to: fcmToken,
    notification: {
      title,
      body,
      icon: '/icons/icon-192x192.png',
      badge: '/icons/badge-72x72.png'
    },
    data: {
      type: 'hydration_reminder',
      timestamp: new Date().toISOString()
    },
    webpush: {
      headers: {
        TTL: '86400' // 24 hours
      },
      notification: {
        icon: '/icons/icon-192x192.png',
        badge: '/icons/badge-72x72.png',
        requireInteraction: false,
        tag: 'hydration-reminder',
        actions: [
          {
            action: 'drink',
            title: 'I drank water! 💧'
          },
          {
            action: 'snooze',
            title: 'Remind me later'
          }
        ]
      }
    }
  };

  try {
    const response = await fetch('https://fcm.googleapis.com/fcm/send', {
      method: 'POST',
      headers: {
        'Authorization': `key=${serverKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(message),
    });

    const result = await response.json();

    if (response.ok && result.success === 1) {
      return { success: true, messageId: result.results?.[0]?.message_id };
    } else {
      return { 
        success: false, 
        error: result.results?.[0]?.error || result.error || 'Unknown FCM error' 
      };
    }
  } catch (error) {
    return { success: false, error: error.message };
  }
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const firebaseServerKey = Deno.env.get('FIREBASE_SERVER_KEY')!

    if (!supabaseUrl || !supabaseServiceKey || !firebaseServerKey) {
      throw new Error('Missing required environment variables')
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    console.log('Starting reminder check at:', new Date().toISOString())

    // Get users eligible for reminders
    const { data: users, error: usersError } = await supabase
      .rpc('get_users_for_reminders')

    if (usersError) {
      throw new Error(`Failed to fetch users: ${usersError.message}`)
    }

    console.log(`Found ${users?.length || 0} users to check`)

    let sentCount = 0
    let skippedCount = 0
    let errorCount = 0

    for (const user of users as UserForReminder[]) {
      try {
        // Check if user is within active hours
        if (!isWithinActiveHours(user.wake_time, user.sleep_time, user.timezone)) {
          console.log(`User ${user.user_id} is outside active hours, skipping`)
          skippedCount++
          continue
        }

        // Check if enough time has passed since last reminder
        const lastReminderTime = new Date(user.last_reminder_sent)
        const now = new Date()
        const timeSinceLastReminder = now.getTime() - lastReminderTime.getTime()
        const intervalMs = user.reminder_interval_minutes * 60 * 1000

        if (timeSinceLastReminder < intervalMs) {
          console.log(`User ${user.user_id} not due for reminder yet, skipping`)
          skippedCount++
          continue
        }

        // Send notification
        const reminder = getRandomReminder()
        const fcmResult = await sendFCMNotification(
          user.fcm_token,
          reminder.title,
          reminder.body,
          firebaseServerKey
        )

        // Log notification attempt
        const { error: logError } = await supabase
          .from('notification_logs')
          .insert({
            user_id: user.user_id,
            notification_type: 'hydration_reminder',
            title: reminder.title,
            body: reminder.body,
            delivery_status: fcmResult.success ? 'sent' : 'failed',
            fcm_message_id: fcmResult.messageId,
            error_message: fcmResult.error,
            retry_count: 0
          })

        if (logError) {
          console.error(`Failed to log notification for user ${user.user_id}:`, logError)
        }

        if (fcmResult.success) {
          console.log(`Sent reminder to user ${user.user_id}`)
          sentCount++
        } else {
          console.error(`Failed to send reminder to user ${user.user_id}:`, fcmResult.error)
          errorCount++
        }

      } catch (userError) {
        console.error(`Error processing user ${user.user_id}:`, userError)
        errorCount++
      }
    }

    const summary = {
      timestamp: new Date().toISOString(),
      totalUsers: users?.length || 0,
      sent: sentCount,
      skipped: skippedCount,
      errors: errorCount
    }

    console.log('Reminder job completed:', summary)

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Reminder job completed successfully',
        summary
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      },
    )

  } catch (error) {
    console.error('Error in send-reminders function:', error)
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      },
    )
  }
})
