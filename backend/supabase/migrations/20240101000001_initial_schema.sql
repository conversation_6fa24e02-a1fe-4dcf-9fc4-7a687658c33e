-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create users table (extends Supabase auth.users)
CREATE TABLE public.users (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    is_active BOOLEAN DEFAULT TRUE NOT NULL,
    fcm_token TEXT,
    timezone TEXT DEFAULT 'UTC' NOT NULL
);

-- Create user_preferences table
CREATE TABLE public.user_preferences (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    daily_goal_ml INTEGER DEFAULT 2000 NOT NULL CHECK (daily_goal_ml > 0),
    wake_time TIME DEFAULT '07:00' NOT NULL,
    sleep_time TIME DEFAULT '22:00' NOT NULL,
    reminder_interval_minutes INTEGER DEFAULT 60 NOT NULL CHECK (reminder_interval_minutes > 0),
    is_notifications_enabled BOOLEAN DEFAULT TRUE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    UNIQUE(user_id)
);

-- Create notification_logs table
CREATE TABLE public.notification_logs (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    notification_type TEXT NOT NULL CHECK (notification_type IN ('hydration_reminder', 'daily_goal_reminder', 'welcome', 'achievement')),
    title TEXT NOT NULL,
    body TEXT NOT NULL,
    sent_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    delivery_status TEXT DEFAULT 'pending' NOT NULL CHECK (delivery_status IN ('pending', 'sent', 'delivered', 'failed', 'retry')),
    fcm_message_id TEXT,
    error_message TEXT,
    retry_count INTEGER DEFAULT 0 NOT NULL
);

-- Create indexes for better performance
CREATE INDEX idx_users_is_active ON public.users(is_active);
CREATE INDEX idx_users_fcm_token ON public.users(fcm_token) WHERE fcm_token IS NOT NULL;
CREATE INDEX idx_user_preferences_user_id ON public.user_preferences(user_id);
CREATE INDEX idx_notification_logs_user_id ON public.notification_logs(user_id);
CREATE INDEX idx_notification_logs_sent_at ON public.notification_logs(sent_at);
CREATE INDEX idx_notification_logs_delivery_status ON public.notification_logs(delivery_status);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_preferences_updated_at BEFORE UPDATE ON public.user_preferences
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security (RLS) policies
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notification_logs ENABLE ROW LEVEL SECURITY;

-- Users can only see and modify their own data
CREATE POLICY "Users can view own profile" ON public.users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON public.users
    FOR INSERT WITH CHECK (auth.uid() = id);

-- User preferences policies
CREATE POLICY "Users can view own preferences" ON public.user_preferences
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own preferences" ON public.user_preferences
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own preferences" ON public.user_preferences
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Notification logs policies (read-only for users)
CREATE POLICY "Users can view own notification logs" ON public.notification_logs
    FOR SELECT USING (auth.uid() = user_id);

-- Service role can access all data (for Edge Functions)
CREATE POLICY "Service role can access all users" ON public.users
    FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

CREATE POLICY "Service role can access all preferences" ON public.user_preferences
    FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

CREATE POLICY "Service role can access all notification logs" ON public.notification_logs
    FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

-- Function to create user profile after signup
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.users (id, timezone)
    VALUES (NEW.id, COALESCE(NEW.raw_user_meta_data->>'timezone', 'UTC'));
    
    INSERT INTO public.user_preferences (user_id)
    VALUES (NEW.id);
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to create user profile after signup
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Function to get users eligible for reminders
CREATE OR REPLACE FUNCTION public.get_users_for_reminders()
RETURNS TABLE (
    user_id UUID,
    fcm_token TEXT,
    timezone TEXT,
    wake_time TIME,
    sleep_time TIME,
    reminder_interval_minutes INTEGER,
    last_reminder_sent TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        u.id,
        u.fcm_token,
        u.timezone,
        up.wake_time,
        up.sleep_time,
        up.reminder_interval_minutes,
        COALESCE(
            (SELECT MAX(nl.sent_at) 
             FROM public.notification_logs nl 
             WHERE nl.user_id = u.id 
             AND nl.notification_type = 'hydration_reminder'
             AND nl.delivery_status IN ('sent', 'delivered')
            ),
            u.created_at
        ) as last_reminder_sent
    FROM public.users u
    JOIN public.user_preferences up ON u.id = up.user_id
    WHERE u.is_active = TRUE
    AND u.fcm_token IS NOT NULL
    AND up.is_notifications_enabled = TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
