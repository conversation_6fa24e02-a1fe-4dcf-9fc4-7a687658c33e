// User and Authentication Types
export interface User {
  id: string;
  created_at: string;
  updated_at: string;
  is_active: boolean;
  fcm_token?: string;
  timezone: string;
}

// User Preferences Types
export interface UserPreferences {
  id: string;
  user_id: string;
  daily_goal_ml: number;
  wake_time: string; // HH:MM format
  sleep_time: string; // HH:MM format
  reminder_interval_minutes: number;
  is_notifications_enabled: boolean;
  created_at: string;
  updated_at: string;
}

// Notification Types
export interface NotificationLog {
  id: string;
  user_id: string;
  notification_type: NotificationType;
  title: string;
  body: string;
  sent_at: string;
  delivery_status: DeliveryStatus;
  fcm_message_id?: string;
  error_message?: string;
  retry_count: number;
}

export enum NotificationType {
  HYDRATION_REMINDER = 'hydration_reminder',
  DAILY_GOAL_REMINDER = 'daily_goal_reminder',
  WELCOME = 'welcome',
  ACHIEVEMENT = 'achievement'
}

export enum DeliveryStatus {
  PENDING = 'pending',
  SENT = 'sent',
  DELIVERED = 'delivered',
  FAILED = 'failed',
  RETRY = 'retry'
}

// Firebase Cloud Messaging Types
export interface FCMMessage {
  token: string;
  notification: {
    title: string;
    body: string;
    icon?: string;
    badge?: string;
  };
  data?: Record<string, string>;
  webpush?: {
    headers?: Record<string, string>;
    data?: Record<string, string>;
    notification?: {
      title?: string;
      body?: string;
      icon?: string;
      badge?: string;
      image?: string;
      tag?: string;
      requireInteraction?: boolean;
      silent?: boolean;
      timestamp?: number;
      actions?: Array<{
        action: string;
        title: string;
        icon?: string;
      }>;
    };
  };
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Utility Types
export interface TimeRange {
  start: string; // HH:MM format
  end: string; // HH:MM format
}

export interface HydrationReminder {
  title: string;
  body: string;
  emoji: string;
}

// Constants
export const DEFAULT_PREFERENCES: Omit<UserPreferences, 'id' | 'user_id' | 'created_at' | 'updated_at'> = {
  daily_goal_ml: 2000, // 2 liters
  wake_time: '07:00',
  sleep_time: '22:00',
  reminder_interval_minutes: 60, // 1 hour
  is_notifications_enabled: true
};

export const HYDRATION_REMINDERS: HydrationReminder[] = [
  {
    title: "Time to hydrate! 💧",
    body: "Your body is calling for some refreshing water!",
    emoji: "💧"
  },
  {
    title: "Water break! 🌊",
    body: "Take a moment to drink some water and feel refreshed!",
    emoji: "🌊"
  },
  {
    title: "Hydration station! 🚰",
    body: "Keep your energy up with a nice glass of water!",
    emoji: "🚰"
  },
  {
    title: "Drink up, buttercup! 🌻",
    body: "Your future self will thank you for staying hydrated!",
    emoji: "🌻"
  },
  {
    title: "H2O time! 💦",
    body: "Let's keep those hydration levels topped up!",
    emoji: "💦"
  }
];

// Error Types
export class AquaBellError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode: number = 500
  ) {
    super(message);
    this.name = 'AquaBellError';
  }
}

export class ValidationError extends AquaBellError {
  constructor(message: string, field?: string) {
    super(message, 'VALIDATION_ERROR', 400);
    this.name = 'ValidationError';
  }
}

export class NotFoundError extends AquaBellError {
  constructor(resource: string) {
    super(`${resource} not found`, 'NOT_FOUND', 404);
    this.name = 'NotFoundError';
  }
}

export class UnauthorizedError extends AquaBellError {
  constructor(message: string = 'Unauthorized') {
    super(message, 'UNAUTHORIZED', 401);
    this.name = 'UnauthorizedError';
  }
}
