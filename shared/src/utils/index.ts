import { TimeRange } from '../types';

/**
 * Check if current time is within user's active hours
 */
export function isWithinActiveHours(
  wakeTime: string,
  sleepTime: string,
  timezone: string = 'UTC',
  currentTime?: Date
): boolean {
  const now = currentTime || new Date();
  
  // Convert to user's timezone
  const userTime = new Date(now.toLocaleString("en-US", { timeZone: timezone }));
  const currentHour = userTime.getHours();
  const currentMinute = userTime.getMinutes();
  const currentTimeInMinutes = currentHour * 60 + currentMinute;

  // Parse wake and sleep times
  const [wakeHour, wakeMinute] = wakeTime.split(':').map(Number);
  const [sleepHour, sleepMinute] = sleepTime.split(':').map(Number);
  
  const wakeTimeInMinutes = wakeHour * 60 + wakeMinute;
  const sleepTimeInMinutes = sleepHour * 60 + sleepMinute;

  // Handle case where sleep time is next day (e.g., wake: 07:00, sleep: 23:00)
  if (sleepTimeInMinutes > wakeTimeInMinutes) {
    return currentTimeInMinutes >= wakeTimeInMinutes && currentTimeInMinutes <= sleepTimeInMinutes;
  } else {
    // Handle case where sleep time crosses midnight (e.g., wake: 22:00, sleep: 06:00)
    return currentTimeInMinutes >= wakeTimeInMinutes || currentTimeInMinutes <= sleepTimeInMinutes;
  }
}

/**
 * Calculate next reminder time based on interval and active hours
 */
export function getNextReminderTime(
  lastReminderTime: Date,
  intervalMinutes: number,
  wakeTime: string,
  sleepTime: string,
  timezone: string = 'UTC'
): Date {
  const nextTime = new Date(lastReminderTime.getTime() + intervalMinutes * 60 * 1000);
  
  // If next time is outside active hours, schedule for next wake time
  if (!isWithinActiveHours(wakeTime, sleepTime, timezone, nextTime)) {
    const tomorrow = new Date(nextTime);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    const [wakeHour, wakeMinute] = wakeTime.split(':').map(Number);
    tomorrow.setHours(wakeHour, wakeMinute, 0, 0);
    
    return tomorrow;
  }
  
  return nextTime;
}

/**
 * Format time string to HH:MM format
 */
export function formatTime(time: string): string {
  const [hours, minutes] = time.split(':');
  return `${hours.padStart(2, '0')}:${minutes.padStart(2, '0')}`;
}

/**
 * Validate time string format (HH:MM)
 */
export function isValidTimeFormat(time: string): boolean {
  const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
  return timeRegex.test(time);
}

/**
 * Convert milliliters to user-friendly format
 */
export function formatVolume(ml: number): string {
  if (ml >= 1000) {
    return `${(ml / 1000).toFixed(1)}L`;
  }
  return `${ml}ml`;
}

/**
 * Generate a random hydration reminder from predefined list
 */
export function getRandomReminder(): { title: string; body: string; emoji: string } {
  const reminders = [
    {
      title: "Time to hydrate! 💧",
      body: "Your body is calling for some refreshing water!",
      emoji: "💧"
    },
    {
      title: "Water break! 🌊",
      body: "Take a moment to drink some water and feel refreshed!",
      emoji: "🌊"
    },
    {
      title: "Hydration station! 🚰",
      body: "Keep your energy up with a nice glass of water!",
      emoji: "🚰"
    },
    {
      title: "Drink up, buttercup! 🌻",
      body: "Your future self will thank you for staying hydrated!",
      emoji: "🌻"
    },
    {
      title: "H2O time! 💦",
      body: "Let's keep those hydration levels topped up!",
      emoji: "💦"
    },
    {
      title: "Splash of wellness! 🌈",
      body: "Every sip brings you closer to your daily goal!",
      emoji: "🌈"
    },
    {
      title: "Aqua alert! 🔔",
      body: "Time for a refreshing water moment!",
      emoji: "🔔"
    },
    {
      title: "Hydration hero! 🦸‍♀️",
      body: "Be your own superhero - drink some water!",
      emoji: "🦸‍♀️"
    }
  ];
  
  return reminders[Math.floor(Math.random() * reminders.length)];
}

/**
 * Validate email format
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Sanitize user input
 */
export function sanitizeString(input: string): string {
  return input.trim().replace(/[<>]/g, '');
}

/**
 * Calculate progress percentage
 */
export function calculateProgress(current: number, goal: number): number {
  if (goal <= 0) return 0;
  return Math.min(Math.round((current / goal) * 100), 100);
}

/**
 * Debounce function for API calls
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * Sleep utility for async operations
 */
export function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Generate UUID v4
 */
export function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}
