# AquaBell Setup Guide

This guide will help you set up the AquaBell hydration reminder app from scratch.

## Prerequisites

- Node.js 18+ installed
- Supabase account
- Firebase account
- Git

## 1. <PERSON><PERSON> and Install

```bash
git clone <your-repo-url>
cd aquabell
npm run install:all
```

## 2. Supabase Setup

### Create a Supabase Project

1. Go to [supabase.com](https://supabase.com)
2. Create a new project
3. Wait for the project to be ready
4. Go to Settings > API to get your keys

### Set up Environment Variables

Copy the backend environment file:
```bash
cp backend/.env.example backend/.env
```

Fill in your Supabase credentials:
```env
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

### Initialize Local Development

```bash
cd backend
npx supabase login
npx supabase init
npx supabase start
```

### Run Migrations

```bash
npm run db:migrate
```

## 3. Firebase Setup

### Create a Firebase Project

1. Go to [Firebase Console](https://console.firebase.google.com)
2. Create a new project
3. Enable Cloud Messaging
4. Generate a web app configuration

### Configure Firebase

1. Go to Project Settings > General
2. Scroll down to "Your apps" and click "Web"
3. Register your app and copy the config
4. Go to Project Settings > Cloud Messaging
5. Generate a new key pair for VAPID

### Set up Environment Variables

Copy the frontend environment file:
```bash
cp frontend/.env.example frontend/.env
```

Fill in your Firebase credentials:
```env
VITE_FIREBASE_API_KEY=your_api_key
VITE_FIREBASE_AUTH_DOMAIN=your_project_id.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your_project_id
VITE_FIREBASE_STORAGE_BUCKET=your_project_id.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
VITE_FIREBASE_APP_ID=your_app_id
VITE_FIREBASE_VAPID_KEY=your_vapid_key
```

Also update the backend .env:
```env
FIREBASE_SERVER_KEY=your_server_key
FIREBASE_PROJECT_ID=your_project_id
```

### Update Service Worker

Edit `frontend/public/firebase-messaging-sw.js` and replace the placeholder config with your actual Firebase config.

## 4. Development

### Start Development Servers

```bash
# Terminal 1 - Backend
npm run dev:backend

# Terminal 2 - Frontend  
npm run dev:frontend
```

### Access the Application

- Frontend: http://localhost:3000
- Supabase Studio: http://localhost:54323

## 5. Testing Notifications

1. Open the app in your browser
2. Allow notification permissions when prompted
3. Go to Settings and ensure notifications are enabled
4. Test the backend function manually:

```bash
curl -X POST http://localhost:54321/functions/v1/send-reminders \
  -H "Authorization: Bearer YOUR_ANON_KEY" \
  -H "Content-Type: application/json"
```

## 6. Deployment

### Deploy Backend

```bash
cd backend
npx supabase functions deploy send-reminders
```

### Deploy Frontend

1. Build the frontend:
```bash
cd frontend
npm run build
```

2. Deploy to Vercel:
```bash
npx vercel --prod
```

### Set up Cron Job

In your Supabase dashboard:
1. Go to Edge Functions
2. Find the `send-reminders` function
3. Set up a cron trigger to run every hour

## 7. Production Environment Variables

Make sure to set up environment variables in your production environments:

### Vercel (Frontend)
Add all `VITE_*` variables in your Vercel project settings.

### Supabase (Backend)
Add `FIREBASE_SERVER_KEY` and `FIREBASE_PROJECT_ID` in your Supabase project settings under Edge Functions.

## Troubleshooting

### Common Issues

1. **Notifications not working**: Check that VAPID key is correctly set and Firebase config is valid
2. **Database errors**: Ensure migrations have been run and RLS policies are correct
3. **CORS errors**: Check that your domain is added to Supabase allowed origins

### Logs

- Frontend: Browser console
- Backend: `npm run logs:send-reminders`
- Supabase: Dashboard > Logs

## Support

If you encounter issues, check:
1. All environment variables are set correctly
2. Firebase and Supabase projects are properly configured
3. Browser supports notifications and service workers
4. Network connectivity for API calls
