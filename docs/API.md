# AquaBell API Documentation

## Overview

AquaBell uses Supabase for backend services and Firebase Cloud Messaging for push notifications.

## Database Schema

### Tables

#### `users`
Extends Supabase auth.users with additional profile information.

```sql
CREATE TABLE public.users (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    is_active BOOLEAN DEFAULT TRUE NOT NULL,
    fcm_token TEXT,
    timezone TEXT DEFAULT 'UTC' NOT NULL
);
```

#### `user_preferences`
Stores user hydration preferences and settings.

```sql
CREATE TABLE public.user_preferences (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    daily_goal_ml INTEGER DEFAULT 2000 NOT NULL CHECK (daily_goal_ml > 0),
    wake_time TIME DEFAULT '07:00' NOT NULL,
    sleep_time TIME DEFAULT '22:00' NOT NULL,
    reminder_interval_minutes INTEGER DEFAULT 60 NOT NULL CHECK (reminder_interval_minutes > 0),
    is_notifications_enabled BOOLEAN DEFAULT TRUE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    UNIQUE(user_id)
);
```

#### `notification_logs`
Tracks all notification attempts and their status.

```sql
CREATE TABLE public.notification_logs (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    notification_type TEXT NOT NULL CHECK (notification_type IN ('hydration_reminder', 'daily_goal_reminder', 'welcome', 'achievement')),
    title TEXT NOT NULL,
    body TEXT NOT NULL,
    sent_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    delivery_status TEXT DEFAULT 'pending' NOT NULL CHECK (delivery_status IN ('pending', 'sent', 'delivered', 'failed', 'retry')),
    fcm_message_id TEXT,
    error_message TEXT,
    retry_count INTEGER DEFAULT 0 NOT NULL
);
```

## Edge Functions

### `send-reminders`

**Endpoint**: `POST /functions/v1/send-reminders`

**Description**: Checks for users who need hydration reminders and sends FCM notifications.

**Schedule**: Runs every hour via cron job

**Request**: No body required

**Response**:
```json
{
  "success": true,
  "message": "Reminder job completed successfully",
  "summary": {
    "timestamp": "2024-01-01T12:00:00.000Z",
    "totalUsers": 150,
    "sent": 45,
    "skipped": 100,
    "errors": 5
  }
}
```

**Logic**:
1. Queries `get_users_for_reminders()` function
2. For each user:
   - Checks if within active hours (wake_time to sleep_time)
   - Checks if enough time has passed since last reminder
   - Sends FCM notification if eligible
   - Logs notification attempt

## Database Functions

### `get_users_for_reminders()`

Returns users eligible for reminders with their preferences and last reminder time.

**Returns**:
```sql
TABLE (
    user_id UUID,
    fcm_token TEXT,
    timezone TEXT,
    wake_time TIME,
    sleep_time TIME,
    reminder_interval_minutes INTEGER,
    last_reminder_sent TIMESTAMP WITH TIME ZONE
)
```

### `handle_new_user()`

Trigger function that creates user profile and default preferences when a new user signs up.

## Row Level Security (RLS)

All tables have RLS enabled with the following policies:

### Users Table
- Users can view/update their own profile
- Service role can access all users

### User Preferences Table  
- Users can view/update their own preferences
- Service role can access all preferences

### Notification Logs Table
- Users can view their own notification logs (read-only)
- Service role can access all logs

## Firebase Cloud Messaging

### Message Format

```json
{
  "to": "fcm_token",
  "notification": {
    "title": "Time to hydrate! 💧",
    "body": "Your body is calling for some refreshing water!",
    "icon": "/icons/icon-192x192.png",
    "badge": "/icons/badge-72x72.png"
  },
  "data": {
    "type": "hydration_reminder",
    "timestamp": "2024-01-01T12:00:00.000Z"
  },
  "webpush": {
    "headers": {
      "TTL": "86400"
    },
    "notification": {
      "icon": "/icons/icon-192x192.png",
      "badge": "/icons/badge-72x72.png",
      "requireInteraction": false,
      "tag": "hydration-reminder",
      "actions": [
        {
          "action": "drink",
          "title": "I drank water! 💧"
        },
        {
          "action": "snooze",
          "title": "Remind me later"
        }
      ]
    }
  }
}
```

### Notification Actions

- **drink**: User confirms they drank water
- **snooze**: User wants to be reminded later
- **default**: Opens the app

## Frontend API Usage

### Authentication

```typescript
// Sign in anonymously
const { data, error } = await supabase.auth.signInAnonymously()

// Sign out
const { error } = await supabase.auth.signOut()
```

### User Profile

```typescript
// Get user profile
const { data, error } = await supabase
  .from('users')
  .select('*')
  .eq('id', userId)
  .single()

// Update FCM token
const { data, error } = await supabase
  .from('users')
  .update({ fcm_token: token })
  .eq('id', userId)
```

### User Preferences

```typescript
// Get preferences
const { data, error } = await supabase
  .from('user_preferences')
  .select('*')
  .eq('user_id', userId)
  .single()

// Update preferences
const { data, error } = await supabase
  .from('user_preferences')
  .update(updates)
  .eq('user_id', userId)
```

### Notification Logs

```typescript
// Get recent notifications
const { data, error } = await supabase
  .from('notification_logs')
  .select('*')
  .eq('user_id', userId)
  .order('sent_at', { ascending: false })
  .limit(10)
```

## Error Handling

### Common Error Codes

- `VALIDATION_ERROR` (400): Invalid input data
- `UNAUTHORIZED` (401): Authentication required
- `NOT_FOUND` (404): Resource not found
- `INTERNAL_ERROR` (500): Server error

### Error Response Format

```json
{
  "success": false,
  "error": "Error message",
  "code": "ERROR_CODE"
}
```

## Rate Limiting

- FCM has a rate limit of 1,000,000 messages per day for free tier
- Supabase has rate limits based on your plan
- Edge Functions have execution time limits (25 seconds for free tier)

## Monitoring

### Metrics to Track

- Notification delivery success rate
- User engagement with notifications
- Active users during different time periods
- Database query performance
- Edge function execution time

### Logging

All operations are logged with appropriate levels:
- `INFO`: Normal operations
- `WARN`: Recoverable errors
- `ERROR`: Critical failures

Logs are available in:
- Supabase Dashboard > Logs
- Browser console (frontend)
- Edge Function logs
