# AquaBell Troubleshooting Guide

This guide covers common issues and their solutions when developing AquaBell locally.

## 🚨 Critical Setup Requirements

### 1. Build Shared Package First (REQUIRED)

**Error**: `Failed to resolve entry for package "@aquabell/shared"`

**Solution**:
```bash
cd shared
npm run build
cd ..
```

**Why this happens**: The frontend and backend depend on compiled TypeScript files from the shared package. Vite cannot resolve the imports without the built files.

### 2. Install Dependencies in Correct Order

**Recommended sequence**:
```bash
# 1. Install root dependencies
npm install

# 2. Install all workspace dependencies
npm run install:all

# 3. Build shared package
cd shared && npm run build && cd ..

# 4. Start development
npm run dev
```

## 🔧 Frontend Issues

### TypeScript Resolution Errors

**Error**: `Cannot find module '@aquabell/shared'`

**Solutions**:
1. Ensure shared package is built: `cd shared && npm run build`
2. Check Vite alias configuration in `frontend/vite.config.ts`
3. Restart TypeScript server in your IDE
4. Clear Vite cache: `rm -rf frontend/node_modules/.vite`

### Environment Variable Issues

**Error**: `Missing Supabase/Firebase configuration`

**Solutions**:
1. Copy environment file: `cp frontend/.env.example frontend/.env`
2. For local development, use these Supabase values:
   ```env
   VITE_SUPABASE_URL=http://127.0.0.1:54321
   VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
   ```
3. For Firebase, replace placeholder values with your actual config

### Service Worker Issues

**Error**: `Failed to register service worker`

**Solutions**:
1. Ensure you're on HTTPS or localhost
2. Check that `frontend/public/sw.js` exists
3. Clear browser cache and reload
4. Check browser console for specific errors

### Icon/Asset 404 Errors

**Error**: `404 Not Found: /icons/icon-192x192.png`

**Solutions**:
1. Icons are created as placeholders during setup
2. For production, replace SVG placeholders with actual PNG icons
3. Use tools like `sharp` or online converters to generate proper icons

### Firebase Configuration Warnings

**Warning**: `Firebase not configured - using placeholder config`

**Expected behavior**: This is normal for local development without Firebase setup
**To fix**: Replace placeholder values in `.env` with your Firebase project config

## 🗄️ Backend Issues

### Supabase CLI Not Found

**Error**: `bash: supabase: command not found`

**Solutions**:
1. Use npx: `npx supabase start`
2. Or install globally: `npm install -g supabase`
3. Check if Docker is running (required for Supabase local)

### Database Migration Issues

**Error**: Migration fails or tables don't exist

**Solutions**:
1. Ensure Supabase is running: `npx supabase start`
2. Run migrations: `npx supabase db push`
3. Reset database if needed: `npx supabase db reset`

### Port Conflicts

**Error**: `Port 54321 already in use`

**Solutions**:
1. Stop existing Supabase: `npx supabase stop`
2. Kill processes using the port: `lsof -ti:54321 | xargs kill`
3. Restart: `npx supabase start`

## 🔔 Notification Issues

### Permission Denied

**Issue**: Notifications don't work

**Solutions**:
1. Check browser notification permissions
2. Ensure HTTPS (required for notifications)
3. Verify Firebase configuration
4. Test with notification test card in settings (dev mode)

### FCM Token Issues

**Error**: `Failed to get FCM token`

**Solutions**:
1. Check VAPID key configuration
2. Ensure Firebase project has Cloud Messaging enabled
3. Verify service worker registration
4. Check browser console for Firebase errors

## 🌐 Network Issues

### CORS Errors

**Error**: `Access to fetch blocked by CORS policy`

**Solutions**:
1. Ensure Supabase local is running on correct port
2. Check API URLs in environment variables
3. Verify Supabase project settings for production

### API Connection Issues

**Error**: `Failed to fetch` or connection timeouts

**Solutions**:
1. Check if backend services are running
2. Verify environment variable URLs
3. Test API endpoints manually
4. Check network connectivity

## 🧪 Development Workflow Issues

### Hot Reload Not Working

**Solutions**:
1. Restart development server
2. Clear browser cache
3. Check for TypeScript errors
4. Ensure file watchers are working

### Build Failures

**Error**: `Build failed with errors`

**Solutions**:
1. Fix TypeScript errors first
2. Ensure shared package is built
3. Check for missing dependencies
4. Clear node_modules and reinstall

## 🔍 Debugging Tools

### Browser Console

Check for these types of errors:
- **Red errors**: Critical issues that break functionality
- **Yellow warnings**: Non-critical issues (often expected in dev)
- **Network tab**: Failed API requests or 404s

### Useful Console Commands

```javascript
// Check if Firebase is initialized
console.log(window.firebase)

// Check Supabase connection
console.log(window.supabase)

// Check service worker status
navigator.serviceWorker.getRegistrations().then(console.log)

// Check notification permission
console.log(Notification.permission)
```

### Log Locations

- **Frontend**: Browser DevTools Console
- **Backend**: Terminal running `npm run dev:backend`
- **Supabase**: `npx supabase logs`
- **Database**: Supabase Studio at http://127.0.0.1:54323

## ✅ Health Check Commands

Run these to verify everything is working:

```bash
# 1. Check if shared package is built
ls shared/dist/

# 2. Check if Supabase is running
curl http://127.0.0.1:54321/health

# 3. Check if frontend is accessible
curl http://localhost:3000

# 4. Test database connection
npx supabase db ping
```

## 🆘 Getting Help

If you're still having issues:

1. **Check the logs**: Look at browser console and terminal output
2. **Verify setup**: Follow the setup guide step by step
3. **Clean install**: Delete node_modules and reinstall
4. **Reset environment**: Stop all services and restart
5. **Check documentation**: Review setup and API docs

### Clean Reset Procedure

```bash
# Stop all services
npx supabase stop
pkill -f "vite\|node"

# Clean dependencies
rm -rf node_modules frontend/node_modules shared/node_modules backend/node_modules

# Reinstall and rebuild
npm install
npm run install:all
cd shared && npm run build && cd ..

# Restart services
npm run dev
```

This should resolve most issues and get you back to a working state.
