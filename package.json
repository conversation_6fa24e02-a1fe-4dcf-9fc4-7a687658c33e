{"name": "aquabell", "version": "1.0.0", "description": "A friendly hydration reminder app with smart notifications", "private": true, "workspaces": ["frontend", "backend", "shared"], "scripts": {"install:all": "npm install && npm run install:frontend && npm run install:backend && npm run install:shared", "install:frontend": "cd frontend && npm install", "install:backend": "cd backend && npm install", "install:shared": "cd shared && npm install", "dev": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && supabase start", "build": "npm run build:shared && npm run build:frontend", "build:frontend": "cd frontend && npm run build", "build:shared": "cd shared && npm run build", "test": "npm run test:frontend && npm run test:shared", "test:frontend": "cd frontend && npm run test", "test:shared": "cd shared && npm run test", "lint": "npm run lint:frontend && npm run lint:shared", "lint:frontend": "cd frontend && npm run lint", "lint:shared": "cd shared && npm run lint", "db:migrate": "cd backend && supabase db push", "db:reset": "cd backend && supabase db reset", "deploy:backend": "cd backend && supabase functions deploy", "deploy:frontend": "cd frontend && npm run build && vercel --prod", "clean": "rm -rf node_modules frontend/node_modules backend/node_modules shared/node_modules", "clean:build": "rm -rf frontend/dist shared/dist"}, "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/theVinesh/aquabell.git"}, "keywords": ["hydration", "reminder", "health", "notifications", "react", "typescript", "supabase", "firebase"], "author": "<PERSON><PERSON>", "license": "MIT"}