{"name": "@aquabell/frontend", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage"}, "dependencies": {"@aquabell/shared": "file:../shared", "@supabase/supabase-js": "^2.38.0", "firebase": "^10.5.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.16.0", "zustand": "^4.4.3"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.3", "@vitest/ui": "^0.34.6", "autoprefixer": "^10.4.16", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "jsdom": "^22.1.0", "postcss": "^8.4.31", "tailwindcss": "^3.3.3", "typescript": "^5.0.2", "vite": "^4.4.5", "vite-plugin-pwa": "^0.16.5", "vitest": "^0.34.6", "workbox-window": "^7.0.0"}}