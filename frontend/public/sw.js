// Service Worker for AquaBell
// Handles push notifications and offline functionality

const CACHE_NAME = 'aquabell-v1'
const urlsToCache = [
  '/',
  '/static/js/bundle.js',
  '/static/css/main.css',
  '/icons/icon-192x192.png',
  '/icons/icon-512x512.png'
]

// Install event - cache resources
self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        console.log('Opened cache')
        return cache.addAll(urlsToCache)
      })
  )
})

// Fetch event - serve from cache when offline
self.addEventListener('fetch', (event) => {
  event.respondWith(
    caches.match(event.request)
      .then((response) => {
        // Return cached version or fetch from network
        return response || fetch(event.request)
      })
  )
})

// Push event - handle incoming push notifications
self.addEventListener('push', (event) => {
  console.log('Push event received:', event)

  let notificationData = {
    title: 'AquaBell Reminder',
    body: 'Time to drink some water! 💧',
    icon: '/icons/icon-192x192.png',
    badge: '/icons/badge-72x72.png',
    tag: 'hydration-reminder',
    requireInteraction: false,
    actions: [
      {
        action: 'drink',
        title: 'I drank water! 💧'
      },
      {
        action: 'snooze',
        title: 'Remind me later'
      }
    ],
    data: {
      timestamp: Date.now(),
      type: 'hydration_reminder'
    }
  }

  // Parse notification data from push payload
  if (event.data) {
    try {
      const payload = event.data.json()
      console.log('Push payload:', payload)
      
      if (payload.notification) {
        notificationData = {
          ...notificationData,
          ...payload.notification
        }
      }
      
      if (payload.data) {
        notificationData.data = {
          ...notificationData.data,
          ...payload.data
        }
      }
    } catch (error) {
      console.error('Error parsing push payload:', error)
    }
  }

  const promiseChain = self.registration.showNotification(
    notificationData.title,
    notificationData
  )

  event.waitUntil(promiseChain)
})

// Notification click event
self.addEventListener('notificationclick', (event) => {
  console.log('Notification click received:', event)

  event.notification.close()

  const action = event.action
  const notificationData = event.notification.data || {}

  if (action === 'drink') {
    // Handle "I drank water" action
    console.log('User clicked: I drank water')
    
    // Send message to main app
    event.waitUntil(
      self.clients.matchAll().then((clients) => {
        clients.forEach((client) => {
          client.postMessage({
            type: 'WATER_LOGGED',
            timestamp: Date.now(),
            amount: 250 // Default glass amount
          })
        })
      })
    )
  } else if (action === 'snooze') {
    // Handle "Remind me later" action
    console.log('User clicked: Remind me later')
    
    // Could schedule another notification or send message to app
    event.waitUntil(
      self.clients.matchAll().then((clients) => {
        clients.forEach((client) => {
          client.postMessage({
            type: 'REMINDER_SNOOZED',
            timestamp: Date.now(),
            snoozeMinutes: 30
          })
        })
      })
    )
  } else {
    // Default action - open the app
    event.waitUntil(
      self.clients.matchAll({
        type: 'window'
      }).then((clientList) => {
        // If app is already open, focus it
        for (let i = 0; i < clientList.length; i++) {
          const client = clientList[i]
          if (client.url === '/' && 'focus' in client) {
            return client.focus()
          }
        }
        
        // If app is not open, open it
        if (self.clients.openWindow) {
          return self.clients.openWindow('/')
        }
      })
    )
  }
})

// Background sync for offline actions
self.addEventListener('sync', (event) => {
  console.log('Background sync event:', event.tag)
  
  if (event.tag === 'water-intake-sync') {
    event.waitUntil(syncWaterIntake())
  }
})

// Function to sync water intake data when back online
async function syncWaterIntake() {
  try {
    // Get pending water intake data from IndexedDB
    const pendingData = await getPendingWaterIntake()
    
    if (pendingData.length > 0) {
      // Send to server
      for (const data of pendingData) {
        await fetch('/api/water-intake', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(data)
        })
      }
      
      // Clear pending data
      await clearPendingWaterIntake()
      console.log('Water intake data synced successfully')
    }
  } catch (error) {
    console.error('Error syncing water intake data:', error)
  }
}

// Helper functions for IndexedDB operations
async function getPendingWaterIntake() {
  // Implementation would use IndexedDB to get pending data
  return []
}

async function clearPendingWaterIntake() {
  // Implementation would clear pending data from IndexedDB
}

// Listen for messages from the main app
self.addEventListener('message', (event) => {
  console.log('Service worker received message:', event.data)
  
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting()
  }
})

console.log('AquaBell Service Worker loaded')
