// Firebase Cloud Messaging Service Worker
// This file is required for Firebase Cloud Messaging to work in the background

importScripts('https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js')
importScripts('https://www.gstatic.com/firebasejs/9.0.0/firebase-messaging-compat.js')

// Initialize Firebase
const firebaseConfig = {
  apiKey: "AIzaSyAl2rLFeLJ0xaeQfT-saRVyaIeDGGwqLE4",
  authDomain: "gym-manager-66633.firebaseapp.com",
  projectId: "gym-manager-66633",
  storageBucket: "gym-manager-66633.firebasestorage.app",
  messagingSenderId: "524699795215",
  appId: "1:524699795215:web:8b5d2ccdb654f07920887a"
}

firebase.initializeApp(firebaseConfig)

// Retrieve Firebase Messaging object
const messaging = firebase.messaging()

// Handle background messages
messaging.onBackgroundMessage((payload) => {
  console.log('Received background message:', payload)

  const notificationTitle = payload.notification?.title || 'AquaBell Reminder'
  const notificationOptions = {
    body: payload.notification?.body || 'Time to drink some water! 💧',
    icon: '/icons/icon-192x192.png',
    badge: '/icons/badge-72x72.png',
    tag: 'hydration-reminder',
    requireInteraction: false,
    actions: [
      {
        action: 'drink',
        title: 'I drank water! 💧'
      },
      {
        action: 'snooze',
        title: 'Remind me later'
      }
    ],
    data: payload.data || {}
  }

  self.registration.showNotification(notificationTitle, notificationOptions)
})
