// Firebase Cloud Messaging Service Worker
// This file is required for Firebase Cloud Messaging to work in the background

importScripts('https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js')
importScripts('https://www.gstatic.com/firebasejs/9.0.0/firebase-messaging-compat.js')

// Initialize Firebase
const firebaseConfig = {
  apiKey: "your_firebase_api_key",
  authDomain: "your_project_id.firebaseapp.com",
  projectId: "your_firebase_project_id",
  storageBucket: "your_project_id.appspot.com",
  messagingSenderId: "your_sender_id",
  appId: "your_firebase_app_id"
}

firebase.initializeApp(firebaseConfig)

// Retrieve Firebase Messaging object
const messaging = firebase.messaging()

// Handle background messages
messaging.onBackgroundMessage((payload) => {
  console.log('Received background message:', payload)

  const notificationTitle = payload.notification?.title || 'AquaBell Reminder'
  const notificationOptions = {
    body: payload.notification?.body || 'Time to drink some water! 💧',
    icon: '/icons/icon-192x192.png',
    badge: '/icons/badge-72x72.png',
    tag: 'hydration-reminder',
    requireInteraction: false,
    actions: [
      {
        action: 'drink',
        title: 'I drank water! 💧'
      },
      {
        action: 'snooze',
        title: 'Remind me later'
      }
    ],
    data: payload.data || {}
  }

  self.registration.showNotification(notificationTitle, notificationOptions)
})
