import { initializeApp } from 'firebase/app'
import { getMessaging, getToken, onMessage, type Messaging } from 'firebase/messaging'

const firebaseConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: import.meta.env.VITE_FIREBASE_APP_ID,
  measurementId: import.meta.env.VITE_FIREBASE_MEASUREMENT_ID
}

const vapidKey = import.meta.env.VITE_FIREBASE_VAPID_KEY

let messaging: Messaging | null = null

export function initializeFirebase() {
  try {
    const app = initializeApp(firebaseConfig)
    
    // Initialize messaging only if supported
    if ('serviceWorker' in navigator && 'Notification' in window) {
      messaging = getMessaging(app)
    }
    
    console.log('Firebase initialized successfully')
  } catch (error) {
    console.error('Error initializing Firebase:', error)
  }
}

export async function requestNotificationPermission(): Promise<string | null> {
  if (!messaging) {
    console.warn('Firebase messaging not available')
    return null
  }

  try {
    // Request notification permission
    const permission = await Notification.requestPermission()
    
    if (permission === 'granted') {
      console.log('Notification permission granted')
      
      // Get FCM token
      const token = await getToken(messaging, { vapidKey })
      console.log('FCM token:', token)
      
      return token
    } else {
      console.log('Notification permission denied')
      return null
    }
  } catch (error) {
    console.error('Error getting notification permission:', error)
    return null
  }
}

export function onMessageListener() {
  if (!messaging) {
    return Promise.reject('Firebase messaging not available')
  }

  return new Promise((resolve) => {
    onMessage(messaging!, (payload) => {
      console.log('Foreground message received:', payload)
      resolve(payload)
    })
  })
}

export async function getFCMToken(): Promise<string | null> {
  if (!messaging) {
    console.warn('Firebase messaging not available')
    return null
  }

  try {
    const token = await getToken(messaging, { vapidKey })
    return token
  } catch (error) {
    console.error('Error getting FCM token:', error)
    return null
  }
}

// Show notification manually (for foreground messages)
export function showNotification(title: string, body: string, options?: NotificationOptions) {
  if ('Notification' in window && Notification.permission === 'granted') {
    const notification = new Notification(title, {
      body,
      icon: '/icons/icon-192x192.png',
      badge: '/icons/badge-72x72.png',
      tag: 'hydration-reminder',
      requireInteraction: false,
      ...options
    })

    // Auto close after 5 seconds
    setTimeout(() => {
      notification.close()
    }, 5000)

    return notification
  }
}

// Check if notifications are supported
export function isNotificationSupported(): boolean {
  return 'Notification' in window && 'serviceWorker' in navigator
}

// Check current notification permission
export function getNotificationPermission(): NotificationPermission {
  if ('Notification' in window) {
    return Notification.permission
  }
  return 'default'
}
