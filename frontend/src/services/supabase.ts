import { createClient } from '@supabase/supabase-js'
import type { Database } from '../types/supabase'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  }
})

// Auth helpers
export const auth = {
  async signInAnonymously() {
    const { data, error } = await supabase.auth.signInAnonymously()
    if (error) throw error
    return data
  },

  async signOut() {
    const { error } = await supabase.auth.signOut()
    if (error) throw error
  },

  async getSession() {
    const { data: { session }, error } = await supabase.auth.getSession()
    if (error) throw error
    return session
  },

  async getUser() {
    const { data: { user }, error } = await supabase.auth.getUser()
    if (error) throw error
    return user
  }
}

// User profile helpers
export const userProfile = {
  async get(userId: string) {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single()
    
    if (error) throw error
    return data
  },

  async update(userId: string, updates: Partial<Database['public']['Tables']['users']['Update']>) {
    const { data, error } = await supabase
      .from('users')
      .update(updates)
      .eq('id', userId)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  async updateFCMToken(userId: string, fcmToken: string) {
    return this.update(userId, { fcm_token: fcmToken })
  }
}

// User preferences helpers
export const userPreferences = {
  async get(userId: string) {
    const { data, error } = await supabase
      .from('user_preferences')
      .select('*')
      .eq('user_id', userId)
      .single()
    
    if (error) throw error
    return data
  },

  async update(userId: string, updates: Partial<Database['public']['Tables']['user_preferences']['Update']>) {
    const { data, error } = await supabase
      .from('user_preferences')
      .update(updates)
      .eq('user_id', userId)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  async create(preferences: Database['public']['Tables']['user_preferences']['Insert']) {
    const { data, error } = await supabase
      .from('user_preferences')
      .insert(preferences)
      .select()
      .single()
    
    if (error) throw error
    return data
  }
}

// Notification logs helpers
export const notificationLogs = {
  async getRecent(userId: string, limit = 10) {
    const { data, error } = await supabase
      .from('notification_logs')
      .select('*')
      .eq('user_id', userId)
      .order('sent_at', { ascending: false })
      .limit(limit)
    
    if (error) throw error
    return data
  },

  async create(log: Database['public']['Tables']['notification_logs']['Insert']) {
    const { data, error } = await supabase
      .from('notification_logs')
      .insert(log)
      .select()
      .single()
    
    if (error) throw error
    return data
  }
}
