import { useNotificationStore } from '../stores/notificationStore'
import LoadingSpinner from './LoadingSpinner'

export default function NotificationBanner() {
  const { 
    showBanner, 
    loading, 
    requestPermission, 
    dismissBanner 
  } = useNotificationStore()

  if (!showBanner) return null

  const handleEnableNotifications = async () => {
    const success = await requestPermission()
    if (success) {
      console.log('Notifications enabled successfully!')
    }
  }

  return (
    <div className="notification-permission-banner m-4 flex items-center justify-between">
      <div className="flex-1 mr-4">
        <h3 className="font-semibold text-white mb-1">
          Stay Hydrated! 💧
        </h3>
        <p className="text-white/90 text-sm">
          Enable notifications to get friendly reminders throughout your day
        </p>
      </div>
      
      <div className="flex gap-2">
        <button
          onClick={handleEnableNotifications}
          disabled={loading}
          className="bg-white text-primary-600 px-4 py-2 rounded-xl font-medium text-sm hover:bg-gray-50 transition-colors duration-200 disabled:opacity-50 flex items-center gap-2"
        >
          {loading && <LoadingSpinner size="small" />}
          Enable
        </button>
        
        <button
          onClick={dismissBanner}
          className="text-white/80 hover:text-white px-2 py-2 rounded-xl transition-colors duration-200"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
    </div>
  )
}
