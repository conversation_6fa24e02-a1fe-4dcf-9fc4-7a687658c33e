import { formatVolume } from '@aquabell/shared'

interface WaterProgressRingProps {
  progress: number // 0-100
  currentIntake: number
  dailyGoal: number
  size?: number
}

export default function WaterProgressRing({ 
  progress, 
  currentIntake, 
  dailyGoal, 
  size = 200 
}: WaterProgressRingProps) {
  const radius = (size - 20) / 2
  const circumference = 2 * Math.PI * radius
  const strokeDasharray = circumference
  const strokeDashoffset = circumference - (progress / 100) * circumference

  return (
    <div className="flex flex-col items-center">
      <div className="relative" style={{ width: size, height: size }}>
        {/* Background circle */}
        <svg
          className="progress-ring absolute inset-0"
          width={size}
          height={size}
        >
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke="#E5E7EB"
            strokeWidth="10"
            fill="transparent"
          />
        </svg>

        {/* Progress circle */}
        <svg
          className="progress-ring absolute inset-0"
          width={size}
          height={size}
        >
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke="url(#gradient)"
            strokeWidth="10"
            fill="transparent"
            strokeDasharray={strokeDasharray}
            strokeDashoffset={strokeDashoffset}
            strokeLinecap="round"
            className="transition-all duration-500 ease-out"
          />
          <defs>
            <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#3B82F6" />
              <stop offset="100%" stopColor="#0EA5E9" />
            </linearGradient>
          </defs>
        </svg>

        {/* Center content */}
        <div className="absolute inset-0 flex flex-col items-center justify-center">
          <div className="text-4xl mb-2 animate-float">
            {progress >= 100 ? '🏆' : '💧'}
          </div>
          <div className="text-2xl font-bold text-gray-800">
            {progress}%
          </div>
          <div className="text-sm text-gray-600">
            {formatVolume(currentIntake)}
          </div>
        </div>
      </div>
    </div>
  )
}
