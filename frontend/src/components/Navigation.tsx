import { Link, useLocation } from 'react-router-dom'
import { useAuthStore } from '../stores/authStore'

export default function Navigation() {
  const location = useLocation()
  const { user } = useAuthStore()

  if (!user) return null

  const isActive = (path: string) => location.pathname === path

  return (
    <nav className="fixed bottom-0 left-0 right-0 bg-white/90 backdrop-blur-md border-t border-gray-200 px-4 py-2 z-50">
      <div className="max-w-md mx-auto flex justify-around">
        <Link
          to="/"
          className={`flex flex-col items-center py-2 px-4 rounded-2xl transition-all duration-200 ${
            isActive('/') 
              ? 'bg-primary-100 text-primary-600' 
              : 'text-gray-500 hover:text-primary-500'
          }`}
        >
          <div className="text-2xl mb-1">
            {isActive('/') ? '💧' : '🌊'}
          </div>
          <span className="text-xs font-medium">Home</span>
        </Link>

        <Link
          to="/settings"
          className={`flex flex-col items-center py-2 px-4 rounded-2xl transition-all duration-200 ${
            isActive('/settings') 
              ? 'bg-primary-100 text-primary-600' 
              : 'text-gray-500 hover:text-primary-500'
          }`}
        >
          <div className="text-2xl mb-1">
            {isActive('/settings') ? '⚙️' : '🔧'}
          </div>
          <span className="text-xs font-medium">Settings</span>
        </Link>
      </div>
    </nav>
  )
}
