import { useEffect, useState } from 'react'
import { usePreferencesStore } from '../stores/preferencesStore'
import { useNotificationStore } from '../stores/notificationStore'
import { formatVolume } from '@aquabell/shared'
import LoadingSpinner from '../components/LoadingSpinner'
import WaterProgressRing from '../components/WaterProgressRing'

export default function HomePage() {
  const { preferences, loadPreferences, loading } = usePreferencesStore()
  const { permission, isSupported } = useNotificationStore()
  const [currentIntake, setCurrentIntake] = useState(0)
  const [lastDrinkTime, setLastDrinkTime] = useState<Date | null>(null)

  useEffect(() => {
    loadPreferences()
  }, [loadPreferences])

  const dailyGoal = preferences?.daily_goal_ml || 2000
  const progress = Math.min(Math.round((currentIntake / dailyGoal) * 100), 100)

  const handleDrinkWater = (amount: number) => {
    setCurrentIntake(prev => Math.min(prev + amount, dailyGoal * 1.5)) // Allow 150% of goal
    setLastDrinkTime(new Date())
  }

  const getMotivationalMessage = () => {
    if (progress === 0) return "Let's start your hydration journey! 🌟"
    if (progress < 25) return "Great start! Keep it up! 💪"
    if (progress < 50) return "You're doing amazing! 🎉"
    if (progress < 75) return "Almost there! You've got this! 🚀"
    if (progress < 100) return "So close to your goal! 🏆"
    return "Goal achieved! You're a hydration hero! 🦸‍♀️"
  }

  const getTimeUntilNextReminder = () => {
    if (!preferences || !lastDrinkTime) return null
    
    const nextReminder = new Date(lastDrinkTime.getTime() + preferences.reminder_interval_minutes * 60 * 1000)
    const now = new Date()
    const diff = nextReminder.getTime() - now.getTime()
    
    if (diff <= 0) return "Due now"
    
    const minutes = Math.floor(diff / (1000 * 60))
    const hours = Math.floor(minutes / 60)
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`
    }
    return `${minutes}m`
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="large" />
      </div>
    )
  }

  return (
    <div className="min-h-screen p-4">
      <div className="max-w-md mx-auto space-y-6">
        {/* Header */}
        <div className="text-center pt-8">
          <h1 className="text-3xl font-bold text-gradient mb-2">
            AquaBell
          </h1>
          <p className="text-gray-600">
            {new Date().toLocaleDateString('en-US', { 
              weekday: 'long', 
              month: 'long', 
              day: 'numeric' 
            })}
          </p>
        </div>

        {/* Progress Ring */}
        <div className="card">
          <WaterProgressRing 
            progress={progress}
            currentIntake={currentIntake}
            dailyGoal={dailyGoal}
          />
          
          <div className="text-center mt-6">
            <p className="text-lg font-medium text-gray-800 mb-2">
              {getMotivationalMessage()}
            </p>
            <div className="flex justify-center items-center gap-4 text-sm text-gray-600">
              <span>{formatVolume(currentIntake)} / {formatVolume(dailyGoal)}</span>
              {progress >= 100 && <span className="text-green-600 font-medium">✓ Goal reached!</span>}
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="card">
          <h2 className="text-lg font-semibold text-gray-800 mb-4">Quick Add</h2>
          <div className="grid grid-cols-2 gap-3">
            {[
              { amount: 250, label: 'Glass', emoji: '🥛' },
              { amount: 500, label: 'Bottle', emoji: '🍼' },
              { amount: 750, label: 'Large', emoji: '🥤' },
              { amount: 1000, label: 'Liter', emoji: '💧' }
            ].map(({ amount, label, emoji }) => (
              <button
                key={amount}
                onClick={() => handleDrinkWater(amount)}
                className="btn-water flex flex-col items-center gap-2 py-4"
              >
                <span className="text-2xl">{emoji}</span>
                <span className="font-medium">{label}</span>
                <span className="text-sm opacity-90">{formatVolume(amount)}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Status Cards */}
        <div className="grid grid-cols-2 gap-4">
          {/* Notification Status */}
          <div className="card">
            <div className="text-center">
              <div className="text-2xl mb-2">
                {isSupported && permission === 'granted' ? '🔔' : '🔕'}
              </div>
              <p className="text-sm font-medium text-gray-800 mb-1">
                Notifications
              </p>
              <p className="text-xs text-gray-600">
                {isSupported 
                  ? permission === 'granted' ? 'Enabled' : 'Disabled'
                  : 'Not supported'
                }
              </p>
            </div>
          </div>

          {/* Next Reminder */}
          <div className="card">
            <div className="text-center">
              <div className="text-2xl mb-2">⏰</div>
              <p className="text-sm font-medium text-gray-800 mb-1">
                Next Reminder
              </p>
              <p className="text-xs text-gray-600">
                {getTimeUntilNextReminder() || 'Not set'}
              </p>
            </div>
          </div>
        </div>

        {/* Recent Activity */}
        {lastDrinkTime && (
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-800 mb-3">Recent Activity</h3>
            <div className="flex items-center gap-3 p-3 bg-green-50 rounded-2xl">
              <div className="text-2xl">✅</div>
              <div>
                <p className="font-medium text-green-800">Water logged!</p>
                <p className="text-sm text-green-600">
                  {lastDrinkTime.toLocaleTimeString('en-US', { 
                    hour: 'numeric', 
                    minute: '2-digit' 
                  })}
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
