@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  body {
    @apply bg-gradient-to-br from-water-50 to-primary-50 min-h-screen;
  }
  
  h1, h2, h3, h4, h5, h6 {
    font-family: 'Poppins', system-ui, sans-serif;
  }
}

@layer components {
  .btn-primary {
    @apply bg-primary-500 hover:bg-primary-600 text-white font-medium py-3 px-6 rounded-2xl transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 active:translate-y-0;
  }
  
  .btn-secondary {
    @apply bg-white hover:bg-gray-50 text-primary-600 font-medium py-3 px-6 rounded-2xl border-2 border-primary-200 hover:border-primary-300 transition-all duration-200 shadow-md hover:shadow-lg;
  }
  
  .btn-water {
    @apply bg-water-500 hover:bg-water-600 text-white font-medium py-3 px-6 rounded-2xl transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 active:translate-y-0;
  }
  
  .card {
    @apply bg-white/80 backdrop-blur-sm rounded-3xl shadow-xl border border-white/20 p-6;
  }
  
  .input-field {
    @apply w-full px-4 py-3 rounded-2xl border-2 border-gray-200 focus:border-primary-400 focus:outline-none transition-colors duration-200 bg-white/50 backdrop-blur-sm;
  }
  
  .progress-ring {
    transform: rotate(-90deg);
    transform-origin: 50% 50%;
  }
  
  .water-drop {
    @apply text-water-500 animate-bounce-slow;
  }
  
  .notification-permission-banner {
    @apply bg-gradient-to-r from-primary-500 to-water-500 text-white p-4 rounded-2xl shadow-lg;
  }
}

@layer utilities {
  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-water-600 bg-clip-text text-transparent;
  }
  
  .bg-glass {
    @apply bg-white/20 backdrop-blur-md border border-white/30;
  }
  
  .shadow-water {
    box-shadow: 0 10px 25px -5px rgba(14, 165, 233, 0.1), 0 4px 6px -2px rgba(14, 165, 233, 0.05);
  }
  
  .animate-water-drop {
    animation: water-drop 2s ease-in-out infinite;
  }
}

@keyframes water-drop {
  0%, 100% {
    transform: translateY(0) scale(1);
  }
  50% {
    transform: translateY(-8px) scale(1.05);
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100 rounded-full;
}

::-webkit-scrollbar-thumb {
  @apply bg-primary-300 rounded-full hover:bg-primary-400;
}

/* Loading animation */
.loading-dots {
  display: inline-block;
}

.loading-dots::after {
  content: '';
  animation: loading-dots 1.5s infinite;
}

@keyframes loading-dots {
  0%, 20% {
    content: '';
  }
  40% {
    content: '.';
  }
  60% {
    content: '..';
  }
  80%, 100% {
    content: '...';
  }
}

/* Notification styles */
.notification-enter {
  opacity: 0;
  transform: translateY(-100%);
}

.notification-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 300ms, transform 300ms;
}

.notification-exit {
  opacity: 1;
  transform: translateY(0);
}

.notification-exit-active {
  opacity: 0;
  transform: translateY(-100%);
  transition: opacity 300ms, transform 300ms;
}
