import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { userPreferences } from '../services/supabase'
import { useAuthStore } from './authStore'
import type { Database } from '../types/supabase'
import { DEFAULT_PREFERENCES } from '@aquabell/shared'

type UserPreferences = Database['public']['Tables']['user_preferences']['Row']
type UserPreferencesUpdate = Database['public']['Tables']['user_preferences']['Update']

interface PreferencesState {
  preferences: UserPreferences | null
  loading: boolean
  error: string | null
}

interface PreferencesActions {
  loadPreferences: () => Promise<void>
  updatePreferences: (updates: Partial<UserPreferencesUpdate>) => Promise<void>
  resetToDefaults: () => Promise<void>
  clearError: () => void
}

export const usePreferencesStore = create<PreferencesState & PreferencesActions>()(
  persist(
    (set, get) => ({
      // State
      preferences: null,
      loading: false,
      error: null,

      // Actions
      loadPreferences: async () => {
        try {
          const { user } = useAuthStore.getState()
          if (!user) throw new Error('No user logged in')

          set({ loading: true, error: null })

          const preferences = await userPreferences.get(user.id)
          
          set({ 
            preferences,
            loading: false 
          })
        } catch (error) {
          console.error('Load preferences error:', error)
          set({ 
            error: error instanceof Error ? error.message : 'Failed to load preferences',
            loading: false 
          })
        }
      },

      updatePreferences: async (updates) => {
        try {
          const { user } = useAuthStore.getState()
          if (!user) throw new Error('No user logged in')

          set({ loading: true, error: null })

          const updatedPreferences = await userPreferences.update(user.id, updates)
          
          set({ 
            preferences: updatedPreferences,
            loading: false 
          })
        } catch (error) {
          console.error('Update preferences error:', error)
          set({ 
            error: error instanceof Error ? error.message : 'Failed to update preferences',
            loading: false 
          })
        }
      },

      resetToDefaults: async () => {
        try {
          const { user } = useAuthStore.getState()
          if (!user) throw new Error('No user logged in')

          set({ loading: true, error: null })

          const defaultUpdates: Partial<UserPreferencesUpdate> = {
            daily_goal_ml: DEFAULT_PREFERENCES.daily_goal_ml,
            wake_time: DEFAULT_PREFERENCES.wake_time,
            sleep_time: DEFAULT_PREFERENCES.sleep_time,
            reminder_interval_minutes: DEFAULT_PREFERENCES.reminder_interval_minutes,
            is_notifications_enabled: DEFAULT_PREFERENCES.is_notifications_enabled
          }

          const updatedPreferences = await userPreferences.update(user.id, defaultUpdates)
          
          set({ 
            preferences: updatedPreferences,
            loading: false 
          })
        } catch (error) {
          console.error('Reset preferences error:', error)
          set({ 
            error: error instanceof Error ? error.message : 'Failed to reset preferences',
            loading: false 
          })
        }
      },

      clearError: () => {
        set({ error: null })
      }
    }),
    {
      name: 'preferences-storage',
      partialize: (state) => ({
        preferences: state.preferences
      })
    }
  )
)
