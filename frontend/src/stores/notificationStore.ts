import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { 
  requestNotificationPermission, 
  getFCMToken, 
  getNotificationPermission,
  isNotificationSupported,
  onMessageListener,
  showNotification
} from '../services/firebase'
import { useAuthStore } from './authStore'
import { userProfile } from '../services/supabase'

interface NotificationState {
  permission: NotificationPermission
  fcmToken: string | null
  isSupported: boolean
  isInitialized: boolean
  showBanner: boolean
  loading: boolean
  error: string | null
}

interface NotificationActions {
  initializeNotifications: () => Promise<void>
  requestPermission: () => Promise<boolean>
  updateFCMToken: () => Promise<void>
  dismissBanner: () => void
  clearError: () => void
}

export const useNotificationStore = create<NotificationState & NotificationActions>()(
  persist(
    (set, get) => ({
      // State
      permission: 'default',
      fcmToken: null,
      isSupported: false,
      isInitialized: false,
      showBanner: false,
      loading: false,
      error: null,

      // Actions
      initializeNotifications: async () => {
        try {
          set({ loading: true, error: null })

          const isSupported = isNotificationSupported()
          const permission = getNotificationPermission()
          
          set({ 
            isSupported,
            permission,
            showBanner: isSupported && permission === 'default',
            isInitialized: true,
            loading: false
          })

          // If permission is already granted, get FCM token
          if (permission === 'granted') {
            await get().updateFCMToken()
          }

          // Listen for foreground messages
          if (isSupported && permission === 'granted') {
            onMessageListener()
              .then((payload: any) => {
                console.log('Received foreground message:', payload)
                
                // Show notification manually for foreground messages
                if (payload.notification) {
                  showNotification(
                    payload.notification.title,
                    payload.notification.body,
                    {
                      data: payload.data,
                      actions: [
                        {
                          action: 'drink',
                          title: 'I drank water! 💧'
                        },
                        {
                          action: 'snooze',
                          title: 'Remind me later'
                        }
                      ]
                    }
                  )
                }
              })
              .catch((error) => {
                console.error('Error listening for messages:', error)
              })
          }

        } catch (error) {
          console.error('Notification initialization error:', error)
          set({ 
            error: error instanceof Error ? error.message : 'Notification initialization failed',
            loading: false 
          })
        }
      },

      requestPermission: async () => {
        try {
          set({ loading: true, error: null })

          const fcmToken = await requestNotificationPermission()
          const permission = getNotificationPermission()

          if (permission === 'granted' && fcmToken) {
            // Update user profile with FCM token
            const { user } = useAuthStore.getState()
            if (user) {
              await userProfile.updateFCMToken(user.id, fcmToken)
            }

            set({ 
              permission,
              fcmToken,
              showBanner: false,
              loading: false
            })

            return true
          } else {
            set({ 
              permission,
              showBanner: false,
              loading: false
            })

            return false
          }
        } catch (error) {
          console.error('Permission request error:', error)
          set({ 
            error: error instanceof Error ? error.message : 'Permission request failed',
            loading: false 
          })
          return false
        }
      },

      updateFCMToken: async () => {
        try {
          const fcmToken = await getFCMToken()
          
          if (fcmToken) {
            // Update user profile with FCM token
            const { user } = useAuthStore.getState()
            if (user) {
              await userProfile.updateFCMToken(user.id, fcmToken)
            }

            set({ fcmToken })
          }
        } catch (error) {
          console.error('FCM token update error:', error)
          set({ 
            error: error instanceof Error ? error.message : 'FCM token update failed'
          })
        }
      },

      dismissBanner: () => {
        set({ showBanner: false })
      },

      clearError: () => {
        set({ error: null })
      }
    }),
    {
      name: 'notification-storage',
      partialize: (state) => ({
        permission: state.permission,
        fcmToken: state.fcmToken,
        showBanner: state.showBanner
      })
    }
  )
)
