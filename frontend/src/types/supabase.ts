export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          is_active: boolean
          fcm_token: string | null
          timezone: string
        }
        Insert: {
          id: string
          created_at?: string
          updated_at?: string
          is_active?: boolean
          fcm_token?: string | null
          timezone?: string
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          is_active?: boolean
          fcm_token?: string | null
          timezone?: string
        }
        Relationships: []
      }
      user_preferences: {
        Row: {
          id: string
          user_id: string
          daily_goal_ml: number
          wake_time: string
          sleep_time: string
          reminder_interval_minutes: number
          is_notifications_enabled: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          daily_goal_ml?: number
          wake_time?: string
          sleep_time?: string
          reminder_interval_minutes?: number
          is_notifications_enabled?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          daily_goal_ml?: number
          wake_time?: string
          sleep_time?: string
          reminder_interval_minutes?: number
          is_notifications_enabled?: boolean
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_preferences_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      notification_logs: {
        Row: {
          id: string
          user_id: string
          notification_type: string
          title: string
          body: string
          sent_at: string
          delivery_status: string
          fcm_message_id: string | null
          error_message: string | null
          retry_count: number
        }
        Insert: {
          id?: string
          user_id: string
          notification_type: string
          title: string
          body: string
          sent_at?: string
          delivery_status?: string
          fcm_message_id?: string | null
          error_message?: string | null
          retry_count?: number
        }
        Update: {
          id?: string
          user_id?: string
          notification_type?: string
          title?: string
          body?: string
          sent_at?: string
          delivery_status?: string
          fcm_message_id?: string | null
          error_message?: string | null
          retry_count?: number
        }
        Relationships: [
          {
            foreignKeyName: "notification_logs_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      get_users_for_reminders: {
        Args: Record<PropertyKey, never>
        Returns: {
          user_id: string
          fcm_token: string
          timezone: string
          wake_time: string
          sleep_time: string
          reminder_interval_minutes: number
          last_reminder_sent: string
        }[]
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
