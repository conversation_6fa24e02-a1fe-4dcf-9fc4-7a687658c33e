{"hash": "0e6eaf0e", "browserHash": "3980f18e", "optimized": {"react": {"src": "../../../../node_modules/react/index.js", "file": "react.js", "fileHash": "721843fc", "needsInterop": true}, "react-dom": {"src": "../../../../node_modules/react-dom/index.js", "file": "react-dom.js", "fileHash": "d66e2382", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../../../node_modules/react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "397fd84b", "needsInterop": true}, "react/jsx-runtime": {"src": "../../../../node_modules/react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "4b055441", "needsInterop": true}, "react-dom/client": {"src": "../../../../node_modules/react-dom/client.js", "file": "react-dom_client.js", "fileHash": "6de237a6", "needsInterop": true}, "react-router-dom": {"src": "../../../../node_modules/react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "42c5d656", "needsInterop": false}, "@supabase/supabase-js": {"src": "../../../../node_modules/@supabase/supabase-js/dist/module/index.js", "file": "@supabase_supabase-js.js", "fileHash": "e8f4e715", "needsInterop": false}, "zustand": {"src": "../../../../node_modules/zustand/esm/index.mjs", "file": "zustand.js", "fileHash": "312c06ac", "needsInterop": false}, "zustand/middleware": {"src": "../../../../node_modules/zustand/esm/middleware.mjs", "file": "zustand_middleware.js", "fileHash": "c5ff5085", "needsInterop": false}, "firebase/app": {"src": "../../../../node_modules/firebase/app/dist/esm/index.esm.js", "file": "firebase_app.js", "fileHash": "2428afa7", "needsInterop": false}, "firebase/messaging": {"src": "../../../../node_modules/firebase/messaging/dist/esm/index.esm.js", "file": "firebase_messaging.js", "fileHash": "df2e4a57", "needsInterop": false}}, "chunks": {"chunk-7A7X2ZMY": {"file": "chunk-7A7X2ZMY.js"}, "browser-OKUMD2QK": {"file": "browser-OKUMD2QK.js"}, "chunk-BXQFRZHP": {"file": "chunk-BXQFRZHP.js"}, "chunk-T36AEHKX": {"file": "chunk-T36AEHKX.js"}, "chunk-HWDFEQAI": {"file": "chunk-HWDFEQAI.js"}, "chunk-ROME4SDB": {"file": "chunk-ROME4SDB.js"}}}