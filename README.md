# 🌊 AquaBell - Hydration Reminder App

A friendly hydration reminder app that helps you stay hydrated throughout the day with timely push notifications.

## 🚀 Features

- 💧 **Smart Hydration Tracking**: Set daily water intake goals
- ⏰ **Intelligent Reminders**: Get notifications during your active hours
- 🔔 **Push Notifications**: Background notifications via Firebase Cloud Messaging
- 📱 **Mobile-First Design**: Responsive, cartoony, and user-friendly interface
- 🔐 **Anonymous Authentication**: No personal data required via Supabase Auth
- ⚡ **Serverless Backend**: Hourly scheduled functions for timely reminders
- 🌐 **Offline Support**: Service worker for offline functionality

## 🏗️ Architecture

```
aquabell/
├── frontend/          # React TypeScript app (Vite + Tailwind)
├── backend/           # Supabase Edge Functions
├── shared/            # Common TypeScript types
└── docs/              # Documentation
```

## 🛠️ Tech Stack

- **Frontend**: React 18, TypeScript, Vite, Tailwind CSS
- **Backend**: Supabase Edge Functions (Deno runtime)
- **Database**: Supabase PostgreSQL
- **Authentication**: Supabase Auth (Anonymous)
- **Push Notifications**: Firebase Cloud Messaging (FCM)
- **Hosting**: Vercel (Frontend) + Supabase (Backend)

## 🚀 Quick Start

### Prerequisites

- Node.js 18+
- Supabase CLI
- Firebase CLI (for FCM setup)

### Environment Setup

1. **Clone and install dependencies**:
```bash
git clone <your-repo-url>
cd aquabell
npm run install:all
```

2. **Set up environment variables**:
```bash
# Frontend
cp frontend/.env.example frontend/.env
# Backend
cp backend/.env.example backend/.env
```

3. **Configure services**:
   - Create a Supabase project at [supabase.com](https://supabase.com)
   - Create a Firebase project at [console.firebase.google.com](https://console.firebase.google.com)
   - Fill in your API keys in the `.env` files

### Development

```bash
# Start frontend development server
npm run dev:frontend

# Start Supabase local development
npm run dev:backend

# Run database migrations
npm run db:migrate
```

## 📱 Usage

1. **First Visit**: Grant notification permissions when prompted
2. **Set Goals**: Configure your daily water intake goal and active hours
3. **Stay Hydrated**: Receive friendly reminders throughout your day
4. **Track Progress**: Monitor your hydration habits (coming soon)

## 🔧 Configuration

### Required Environment Variables

**Frontend (.env)**:
```
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
VITE_FIREBASE_API_KEY=your_firebase_api_key
VITE_FIREBASE_PROJECT_ID=your_firebase_project_id
VITE_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
VITE_FIREBASE_APP_ID=your_firebase_app_id
```

**Backend (.env)**:
```
SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
FIREBASE_SERVER_KEY=your_firebase_server_key
```

## 🚀 Deployment

### Frontend (Vercel)
```bash
npm run build:frontend
npm run deploy:frontend
```

### Backend (Supabase)
```bash
npm run deploy:backend
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

MIT License - see [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Built with ❤️ using Supabase and Firebase
- Icons and illustrations from [your-source]
- Inspired by the importance of staying hydrated! 💧
